import ReusableCarousel from "@/components/common/ReusableCarousel";
import { newbieMessages } from "@/data/newbie-messages";
import { NewbieMessage } from "@/types";
import Image from "next/image";

const NewbieMessages = () => {
  const renderNewbieMessage = (message: NewbieMessage) => {
    return (
      <div className="relative h-[19.5rem] w-72 flex-shrink-0 cursor-pointer overflow-hidden rounded-lg">
        <Image src={message.image} alt={message.title} fill className="absolute inset-0 object-cover" />
        <div className="absolute inset-0 flex flex-col justify-between px-4 pt-4 pb-8">
          <p className="text-lg font-semibold">{message.title}</p>
          <p className="text-xs font-light">{`${message.count.toLocaleString()} message${message.count > 1 ? "s" : ""}`}</p>
        </div>
      </div>
    );
  };

  return (
    <ReusableCarousel<NewbieMessage>
      title="Newbie?.. Start from here !!"
      items={newbieMessages}
      renderItem={renderNewbieMessage}
      itemWidth="basis-[70%] sm:basis-[33.5%] xl:basis-[20.1%]"
    />
  );
};

export default NewbieMessages;
