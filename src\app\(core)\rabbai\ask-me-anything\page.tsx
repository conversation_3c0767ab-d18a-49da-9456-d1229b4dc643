"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { askMeAnythingSchema } from "@/schemas/rabbai";
import { AskMeAnythingFormData } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";

const AskMeAnything = () => {
  const router = useRouter();
  const form = useForm<AskMeAnythingFormData>({
    resolver: zodResolver(askMeAnythingSchema),
    defaultValues: {
      search: "",
    },
  });

  const { getValues } = form;

  const handleAskRabbai = (data: AskMeAnythingFormData) => {
    router.push("/rabbai/ask-me-anything/1");
  };

  return (
    <div className="w-full px-[1rem] sm:px-[2rem] md:px-[4rem] lg:w-[30rem] xl:w-[40rem]">
      <div className="mt-[1rem] mb-10 lg:mt-[3rem]">
        <p className="text-center text-[1.25rem] font-bold md:text-2xl">Ask me Anything</p>
        <p className="mt-2 text-center font-light text-[#FFFFFFB0]">
          Ask me anything you would like to know based on messages you have or are listening to.
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleAskRabbai)} className="space-y-5">
          <FormField
            control={form.control}
            name="search"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea className="mt-5 h-[10rem] rounded-[8px]" placeholder="Type Anything" {...field}></Textarea>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            className="bg-primary mt-5 h-[3rem] w-full font-semibold text-[#191928] disabled:bg-[#E9D7FE4D]"
            type="submit"
            disabled={!getValues("search")}
          >
            Submit Search
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default AskMeAnything;
