"use client";
import arrowLeft from "@/assets/icons/arrow-left.svg";
import allMessages from "@/assets/images/all-messages.png";
import FilterDialog from "@/components/common/FilterDialog";
import SearchBar from "@/components/common/SearchBar";
import PlayStyleIcon from "@/components/icons/PlayStyle";
import FilterComponent from "@/components/messages/MessagesFilter";
import MessagesForYou from "@/components/messages/MessagesForYou";
import RecentlyAdded from "@/components/messages/RecentlyAdded";
import { Button } from "@/components/ui/button";
import { useMobileQuery } from "@/hooks/useMobileQuery";
import { cn } from "@/lib/utils";
import { Search, SlidersVertical } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

const MessagesPage = () => {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const isMobile = useMobileQuery();

  const showSearch = () => {
    setIsSearchActive(true);
  };

  return (
    <div>
      <div className="relative flex items-center justify-between">
        <Image src={allMessages} alt="messages" className="z-0 h-30 w-full" />

        <div className="absolute flex w-full items-center justify-between px-5 sm:px-8">
          <div className="flex items-center gap-2">
            <Image src={arrowLeft} alt="arrow-left-icon" className="size-6 font-thin" />
            <h1 className={cn("text-xl font-bold lg:text-[1.75rem]", isSearchActive && isMobile && "hidden")}>All Messages</h1>
          </div>

          <div className="flex items-center gap-2 md:gap-5">
            {isSearchActive ? (
              <SearchBar
                autoFocus
                className="w-full md:w-100 xl:w-134"
                placeholder="Search for Messages"
                onSubmit={() => setIsSearchActive(false)}
                onClose={() => setIsSearchActive(false)}
              />
            ) : (
              <Button
                size="icon"
                className="bg-off-white/10 flex size-7 cursor-pointer items-center justify-center rounded-full md:size-10"
                onClick={showSearch}
              >
                <Search className="size-3 text-white md:size-5" />
              </Button>
            )}

            <FilterDialog
              Trigger={
                <div className="bg-off-white/10 flex size-7 cursor-pointer items-center justify-center rounded-full md:size-10">
                  <SlidersVertical className="size-3 text-white md:size-5" />
                </div>
              }
              Title="Filter By"
            >
              <FilterComponent />
            </FilterDialog>

            <div className="hidden cursor-pointer items-center gap-2 sm:flex">
              <div className="bg-off-white/10 flex size-7 items-center justify-center rounded-full md:size-10.5">
                <div className="flex size-7 items-center justify-center rounded-full bg-[#B88C00]/12 md:size-7.5">
                  <PlayStyleIcon className="size-3 text-white md:size-5" />
                </div>
              </div>
              <p className="text-xxs sm:text-xs">Play All Messages</p>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4 px-5 lg:px-12">
        <RecentlyAdded />
        <MessagesForYou />
      </div>
    </div>
  );
};

export default MessagesPage;
