"use client";

import DownloadIcon from "@/components/icons/DownloadPopular";
import PlayIcon from "@/components/icons/Play";
import { getNumberInCompactFormat } from "@/lib/utils";
import Image from "next/image";
import { CgMore } from "react-icons/cg";
import { MessageListItemProps } from "../types";

const MessageListItem = ({ message, index }: MessageListItemProps) => {
  return (
    <div
      className={`flex items-center justify-between ${index % 2 === 0 ? "bg-gradient-to-r from-[#FFFFFF03] via-[#FFFFFF1A] to-[#FFFFFF00]" : ""} py-4 text-[#B3B3B3]`}
      key={message.id}
    >
      <div className="flex w-[60%] items-center gap-4 md:w-[40%]">
        <div className="relative">
          <Image
            src={message.image}
            alt={`${message.messageTitle} image`}
            className="min-h-32 w-[6.25rem] min-w-25 rounded-[8px] object-cover sm:min-h-auto lg:h-[4.7rem]"
          />

          <div className="xs:hidden absolute top-12 left-8 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-black">
            <PlayIcon />
          </div>
        </div>
        <div className="space-y-1">
          <p className="text-md font-semibold text-white md:text-lg">{message.messageTitle}</p>
          <p className="text-xs font-light md:text-sm">{message.minister.name}</p>

          <p className="block w-48 text-sm md:hidden">{message.meetingName}</p>
          <p className="block text-sm md:hidden">{`${getNumberInCompactFormat(message.numberOfListens)} listen${message.numberOfListens > 1 ? "s" : ""}`}</p>
          <p className="block text-xs md:hidden">{message.duration}</p>
        </div>
      </div>

      <div className="hidden w-[20%] flex-col justify-between gap-2 text-sm md:flex lg:w-[40%] lg:flex-row lg:items-center lg:text-base">
        <p className="lg:w-[40%]">{message.meetingName}</p>
        <p className="lg:w-[20%]">{`${getNumberInCompactFormat(message.numberOfListens)} listen${message.numberOfListens > 1 ? "s" : ""}`}</p>
        <p className="lg:w-[20%]">{message.duration}</p>
      </div>

      <div className="xs:flex hidden w-[20%] items-center justify-center gap-6">
        <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A]">
          <PlayIcon />
        </div>
        <div className="hidden h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A] xl:flex">
          <DownloadIcon />
        </div>
        <div className="hidden h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#D6D6D61A] xl:flex">
          <CgMore className="h-[0.875rem] w-[0.875rem]" />
        </div>
      </div>
    </div>
  );
};

export default MessageListItem;
