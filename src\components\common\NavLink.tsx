"use client";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ComponentProps } from "react";

const NavLink = (props: ComponentProps<typeof Link>) => {
  const pathname = usePathname();
  const paths = pathname.split("/");

  return (
    <Link
      {...props}
      href={props.href}
      className={cn(
        "flex items-center rounded-[6px] border border-transparent",
        props.className,
        paths[1] === String(props.href).slice(1) && "text-white",
      )}
    />
  );
};

export default NavLink;
