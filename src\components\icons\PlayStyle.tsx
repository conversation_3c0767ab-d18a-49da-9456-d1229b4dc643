import { SVGProps } from "react";

const PlayStyleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      stroke="#fff"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.001}
      d="M20.7841 11.3858L7.08212 19.6877C6.27544 20.174 5.23828 19.595 5.23828 18.6456V9.38272C5.23828 5.34178 9.58282 2.81765 13.0746 4.83232L18.3641 7.88908L20.7726 9.27851C21.5678 9.75323 21.5793 10.9111 20.7841 11.3858Z"
      fill="white"
    />
    <path
      d="M21.4761 18.1708L16.8089 20.8802L12.1532 23.578C10.4823 24.539 8.59233 24.3422 7.22097 23.3696C6.55258 22.9064 6.63325 21.8759 7.33621 21.4591L21.9832 12.6362C22.6746 12.2194 23.585 12.613 23.7118 13.412C23.9999 15.2067 23.2623 17.1403 21.4761 18.1708Z"
      fill="white"
    />
  </svg>
);

export default PlayStyleIcon;
