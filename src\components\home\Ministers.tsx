import { ministers } from "@/data/ministers";
import Image from "next/image";

const Ministers = () => {
  return (
    <section className="mx-0 mt-12 sm:mx-10">
      <p className="text-lg font-bold sm:text-2xl">Our Ministers</p>
      <div className="mt-8 grid grid-cols-2 gap-8 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-7">
        {ministers.map(minister => (
          <div key={minister.id} className="h-[9.4rem] w-[9.4rem] justify-self-center overflow-hidden rounded-[18px]">
            <Image src={minister.image} alt={`${minister.name} image`} className="h-full w-full object-cover" />
          </div>
        ))}
      </div>
    </section>
  );
};

export default Ministers;
