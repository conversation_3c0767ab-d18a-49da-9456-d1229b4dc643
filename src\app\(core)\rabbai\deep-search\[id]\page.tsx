import voiceCircleIcon from "@/assets/icons/voice-circle.svg";
import { But<PERSON> } from "@/components/ui/button";
import { deepSearchResult } from "@/data/rabbai";
import { Dot } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { use } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const DeepSearchItem = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = use(params);

  return (
    <div className="w-full px-[1rem] pt-[2rem] sm:px-[2rem] md:px-[4rem] lg:w-[45rem]">
      <div>
        <p className="text-center text-[1.25rem] font-bold md:text-2xl">RabbAI Deep Search</p>
        <p className="mt-2 text-center font-light text-[#FFFFFFB0]">Here is a draft of your study guide based on your detailed description</p>
      </div>
      <div className="mt-4 rounded-tr-[22px] rounded-br-[22px] rounded-bl-[22px] border border-[#FFFFFF24] bg-[#FFFFFF0D] px-4 py-6">
        {/* <div className="space-y-4">
          {deepSearchResult.map(result => (
            <div key={result.id} className="space-y-2 rounded-[4px] bg-[#1717174D] px-4 py-5">
              <div className="flex items-center gap-2">
                <Image src={result.image} alt={`${result.topic} image`} className="size-12 rounded-[2.25px] object-cover" />
                <div>
                  <p className="text-lg font-semibold">{result.topic}</p>
                  <div className="flex items-center">
                    <span className="text-sm text-[#9D9D9C]">{result.minister}</span>
                    <Dot className="size-2" />
                    <span className="text-sm font-medium">{result.meeting}</span>
                  </div>
                </div>
              </div>
              <p className="text-sm leading-[160%]">{result.summary}</p>
              <Button className="h-12 w-full gap-3 rounded-[5px] bg-[#FFFFFF12] text-sm font-normal text-white">
                <p>Listen Now</p>
                <Image src={voiceCircleIcon} alt="Listen Now Icon" width={20} height={20} />
              </Button>
            </div>
          ))}
        </div> */}
        <Accordion type="single" collapsible className="space-y-4">
          {deepSearchResult.map(result => (
            <AccordionItem
              value={String(result.id)}
              key={result.id}
              className="space-y-2 rounded-[4px] bg-[#d526264d] px-4 py-5"
              defaultValue={String(result.id)}
            >
              <AccordionTrigger className="items-center">
                <div className="flex items-center gap-2">
                  <Image src={result.image} alt={`${result.topic} image`} className="size-12 rounded-[2.25px] object-cover" />
                  <div>
                    <p className="text-lg font-semibold">{result.topic}</p>
                    <div className="flex items-center">
                      <span className="text-sm text-[#9D9D9C]">{result.minister}</span>
                      <Dot className="size-2" />
                      <span className="text-sm font-medium">{result.meeting}</span>
                    </div>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="flex flex-col gap-4 text-balance">
                <p className="text-sm leading-[160%]">{result.summary}</p>
                <Button className="h-12 w-full gap-3 rounded-[5px] bg-[#FFFFFF12] text-sm font-normal text-white">
                  <p>Listen Now</p>
                  <Image src={voiceCircleIcon} alt="Listen Now Icon" width={20} height={20} />
                </Button>
              </AccordionContent>
            </AccordionItem>
            // <div key={result.id} className="space-y-2 rounded-[4px] bg-[#1717174D] px-4 py-5">
            //   <div className="flex items-center gap-2">
            //     <Image src={result.image} alt={`${result.topic} image`} className="size-12 rounded-[2.25px] object-cover" />
            //     <div>
            //       <p className="text-lg font-semibold">{result.topic}</p>
            //       <div className="flex items-center">
            //         <span className="text-sm text-[#9D9D9C]">{result.minister}</span>
            //         <Dot className="size-2" />
            //         <span className="text-sm font-medium">{result.meeting}</span>
            //       </div>
            //     </div>
            //   </div>
            //   <p className="text-sm leading-[160%]">{result.summary}</p>
            //   <Button className="h-12 w-full gap-3 rounded-[5px] bg-[#FFFFFF12] text-sm font-normal text-white">
            //     <p>Listen Now</p>
            //     <Image src={voiceCircleIcon} alt="Listen Now Icon" width={20} height={20} />
            //   </Button>
            // </div>
          ))}
        </Accordion>
      </div>
      <div className="mt-[4rem] flex h-24 items-center gap-4 bg-[#FFFFFF05] px-4 lg:-mx-[6rem] xl:-mx-[12rem]">
        <Button className="h-12 flex-1 gap-3 rounded-[8px] border-[#FFFFFF63] bg-[#1E1C2B] text-white">Cancel</Button>
        <Link
          href="/rabbai/study-guide"
          className="flex h-12 flex-1 items-center justify-center gap-3 rounded-[8px] bg-white font-medium text-[#1E1C2B]"
        >
          Search Again
        </Link>
      </div>
    </div>
  );
};

export default DeepSearchItem;
