import RecentlyAddedCard from "../common/RecentlyAddedCard";
import { popularMessages } from "@/data/popular";
import ReusableCarousel from "../common/ReusableCarousel";
import { Message } from "../common/types";

const RecentlyAdded = () => {
  return (
    <ReusableCarousel<Message>
      title="Recently Added"
      items={popularMessages}
      renderItem={RecentlyAddedCard}
      itemWidth="basis-[70%] sm:basis-[33.5%] xl:basis-[20.1%]"
    />
  );
};

export default RecentlyAdded;
