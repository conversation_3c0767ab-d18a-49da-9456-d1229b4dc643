import { Dialog, DialogClose, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { X } from "lucide-react";
import { ReactNode } from "react";

const FilterDialog = ({ Trigger, children, Title }: { Trigger: ReactNode; children: ReactNode; Title?: string }) => {
  return (
    <Dialog>
      <DialogTrigger>{Trigger}</DialogTrigger>

      <DialogContent className="border-0.1 right-0 left-auto h-full translate-x-0 overflow-y-auto border-l border-l-[#D0D5DD]/16 bg-radial from-[#020213]/10 from-0% to-[#020213]/30 to-100% px-0 pb-0 sm:w-full">
        {/* TODO: Reposition close button and remove ring on dialog */}
        <DialogClose className="absolute top-5 right-5 z-150">
          <div className="flex size-9 items-center justify-center rounded-full bg-white">
            <X className="h-5 w-5 font-bold text-[#1e1c2b]" />
            <span className="sr-only">Close</span>
          </div>
        </DialogClose>

        {/* // For accessibility, radix doesn't permit omitting a dialog title. To hide it from the screen, instead, wrap with <VisuallyHidden>  */}
        <VisuallyHidden>
          <DialogTitle>{Title}</DialogTitle>
        </VisuallyHidden>

        {children}
      </DialogContent>
    </Dialog>
  );
};

export default FilterDialog;
