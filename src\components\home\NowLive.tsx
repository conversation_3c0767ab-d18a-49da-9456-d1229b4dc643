import nowLiveImage from "@/assets/images/now-live.png";
import LiveBadge from "@/components/home/<USER>";
import ListenNowIcon from "@/components/icons/ListenNow";
import StreamVideoIcon from "@/components/icons/StreamVideo";
import { Button } from "@/components/ui/button";
import { liveMeeting } from "@/data/live-meeting";
import Image from "next/image";

const NowLive = () => {
  if (liveMeeting === null) return null;
  return (
    <section className="mt-12 flex flex-col justify-between rounded-[8px] bg-[#3333331F] md:h-[8.3rem] md:flex-row lg:px-8">
      <div className="flex items-center gap-2 py-4 sm:gap-8 md:px-8">
        <Image src={nowLiveImage} alt="Now Live" className="h-20 w-20 object-cover lg:h-[6rem] lg:w-[9.25rem]" />
        <div className="flex w-full justify-between gap-4">
          <div className="flex flex-col items-start gap-4">
            <p className="text-sm font-bold lg:text-xl xl:text-[1.75rem]">{liveMeeting.meetingName}</p>
            <p className="text-xs font-light text-white/60 lg:text-base">{`${liveMeeting.numberOfListens.toLocaleString()} listening`}</p>
          </div>

          <LiveBadge className="lg:mt-1.5" />
        </div>
      </div>

      <div className="mr-16 flex items-center gap-6">
        <Button className="h-8 text-xs lg:h-12 lg:w-[9.5rem] lg:text-base">
          <ListenNowIcon />
          <p>Listen Now</p>
        </Button>
        <Button className="h-8 bg-white/[8%] text-xs text-white lg:h-12 lg:w-[9.5rem] lg:text-base">
          <StreamVideoIcon />
          <p>Stream Video</p>
        </Button>
      </div>
    </section>
  );
};

export default NowLive;
