import arrowLeft from "@/assets/icons/arrow-left.svg";
import arrowRight from "@/assets/icons/arrow-right.svg";
import { Button } from "@/components/ui/button";
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { useEffect, useState } from "react";
import { CarouselItemData, ReusableCarouselProps } from "@/components/common/types";
import Image from "next/image";

const ReusableCarousel = <T extends CarouselItemData>({ title, items, renderItem, itemWidth }: ReusableCarouselProps<T>) => {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  useEffect(() => {
    if (!api || !autoScroll) return;

    const interval = setInterval(() => {
      api.scrollNext();
    }, 3000);

    return () => clearInterval(interval);
  }, [api, autoScroll]);

  return (
    <section className="mt-12 sm:mx-10">
      <div className="flex items-center justify-between">
        <p className="text-lg font-bold sm:text-2xl">{title}</p>
        {api && (
          <div className="hidden gap-2 sm:flex">
            <Button
              size="icon"
              className="size-5 rounded-full border border-[#E6EAF466] bg-[#F2F4FD0A] sm:size-8"
              onClick={() => api.scrollPrev()}
              aria-label="Previous slide"
            >
              <Image src={arrowLeft} alt="arrow-left-icon" />
            </Button>
            <Button
              size="icon"
              className="size-5 rounded-full border border-[#E6EAF466] bg-[#F2F4FD0A] sm:size-8"
              onClick={() => api.scrollNext()}
              aria-label="Next slide"
            >
              <Image src={arrowRight} alt="arrow-right-icon" />
            </Button>
          </div>
        )}
      </div>
      <div className="mt-8">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          setApi={setApi}
        >
          <CarouselContent>
            {items.map(item => (
              <CarouselItem
                key={item.id}
                className={`${itemWidth}`}
                onMouseOver={() => setAutoScroll(false)}
                onMouseLeave={() => setAutoScroll(true)}
              >
                {renderItem(item)}
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
};

export default ReusableCarousel;
