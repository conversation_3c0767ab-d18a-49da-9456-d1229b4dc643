"use client";
import MessagesForYou from "@/components/messages/MessagesForYou";
import RecentlyAdded from "@/components/messages/RecentlyAdded";
import RecentlyListened from "@/components/messages/RecentlyListened";
import SeriesForYou from "@/components/messages/SeriesForYou";
import { Search, SlidersVertical } from "lucide-react";

const MessagesPage = () => {
  return (
    <div>
      <div className="flex h-30 items-center justify-between bg-[url(@/assets/images/messages.png)] px-6 md:h-50 md:px-12">
        <div className="flex flex-col">
          <h1 className="text-2xl font-bold md:text-[2.63rem]">Messages</h1>
          <p className="hidden text-lg text-white/60 md:block md:text-2xl">134 messages and 10 series</p>
        </div>

        <div className="flex items-center gap-2 md:gap-5">
          <div className="flex size-8 items-center justify-center rounded-full bg-[#D9D9D9]/10 md:size-14">
            <Search className="size-4 text-white md:size-7" />
          </div>

          <div className="flex size-8 items-center justify-center rounded-full bg-[#D9D9D9]/10 md:size-14">
            <SlidersVertical className="size-4 text-white md:size-7" />
          </div>
        </div>
      </div>

      <div className="px-5 lg:px-12">
        <RecentlyListened />
        <RecentlyAdded />
        <SeriesForYou />
        <MessagesForYou preview />
      </div>
    </div>
  );
};

export default MessagesPage;
