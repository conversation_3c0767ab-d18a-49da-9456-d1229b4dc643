import microphoneIcon from "@/assets/icons/microphone.svg";
import searchIcon from "@/assets/icons/search.svg";
import { SearchBarProps } from "@/components/common/types";
import { cn } from "@/lib/utils";
import isHotkey from "is-hotkey";
import Image from "next/image";
import { Input } from "../ui/input";
import { useEffect } from "react";

const SearchBar = ({ className, placeholder = "Search", rightIcon, onSubmit, onClose, autoFocus = false }: SearchBarProps) => {
  const onInputKeyDown = (evt: React.KeyboardEvent<HTMLDivElement>) => {
    if (isHotkey("esc", evt)) {
      onClose();
    }

    if (isHotkey("enter", evt)) {
      onClose();
      onSubmit();
    }
  };

  useEffect(() => {}, []);

  return (
    <div className={cn("relative h-[2rem] sm:h-[2.6rem]", className)}>
      <Input
        {...(autoFocus && { autoFocus })}
        className="h-full w-full border border-[#E5E7EB3B] bg-[#E5E7EB0A] px-10 placeholder:font-light"
        placeholder={placeholder}
        onKeyDown={evt => onInputKeyDown(evt)}
      />
      <Image src={searchIcon} alt="Search Icon" className="absolute top-1/2 left-4 -translate-y-1/2 text-[#BEBEBE]/80" />
      {rightIcon && <Image src={rightIcon ?? microphoneIcon} alt="Microphone Icon" className="absolute top-1/2 right-4 -translate-y-1/2" />}
    </div>
  );
};

export default SearchBar;
