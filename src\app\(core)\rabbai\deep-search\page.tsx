"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { deepSearchSchema } from "@/schemas/rabbai";
import { DeepSearchFormData } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";

const DeepSearch = () => {
  const router = useRouter();
  const form = useForm<DeepSearchFormData>({
    resolver: zodResolver(deepSearchSchema),
    defaultValues: {
      search: "",
    },
  });

  const { getValues } = form;

  const handleAskRabbai = (data: DeepSearchFormData) => {
    router.push("/rabbai/deep-search/1");
  };

  return (
    <div className="w-full px-[1rem] sm:px-[2rem] md:px-[4rem] lg:w-[30rem] xl:w-[40rem]">
      <div className="mt-[1rem] mb-10 lg:mt-[3rem]">
        <p className="text-center text-[1.25rem] font-bold md:text-2xl">RabbAI Deep Search</p>
        <p className="mt-2 text-center font-light text-[#FFFFFFB0]">
          Give keywords to find messages related to your search exclusively using content from Fountain Stream.
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleAskRabbai)} className="space-y-5">
          <FormField
            control={form.control}
            name="search"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea className="mt-5 h-[10rem] rounded-[8px]" placeholder="Type Anything" {...field}></Textarea>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            className="bg-primary mt-5 h-[3rem] w-full font-semibold text-[#191928] disabled:bg-[#E9D7FE4D]"
            type="submit"
            disabled={!getValues("search")}
          >
            Submit Search
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default DeepSearch;
