import { IconProps } from "@/components/common/types";

const RabbaiIcon = ({ isActive = false, ...svgProps }: IconProps) => {
  if (isActive) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={svgProps.height || 24}
        height={svgProps.height || 24}
        fill="none"
        viewBox="0 0 24 24"
        {...svgProps}
      >
        <path
          fill="#fff"
          d="M15.59 12.4v4.07c0 .36-.04.7-.13 1.01-.37 1.47-1.59 2.39-3.27 2.39H9.47l-3.02 2.01a.671.671 0 0 1-1.05-.56v-1.45c-1.02 0-1.87-.34-2.46-.93-.6-.6-.94-1.45-.94-2.47V12.4c0-1.9 1.18-3.21 3-3.38.13-.01.26-.02.4-.02h6.79c2.04 0 3.4 1.36 3.4 3.4"
        ></path>
        <path
          fill="#fff"
          stroke="#fff"
          strokeWidth="1.5"
          d="m20.303 13.896-.007.007c-.561.569-1.396.927-2.456.946V12.4a5.645 5.645 0 0 0-5.65-5.65H5.75v-.5c0-1.936 1.564-3.5 3.5-3.5h8.5c1.936 0 3.5 1.564 3.5 3.5v5.1c0 1.103-.362 1.97-.947 2.546Z"
        ></path>
      </svg>
    );
  }
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={svgProps.height || 24} height={svgProps.height || 24} fill="none" {...svgProps}>
      <path
        fill="#6E6E6E"
        d="M16.9 19.01c-.31 0-.62-.09-.89-.27l-.96-.63a.755.755 0 0 1-.31-.83c.07-.23.1-.5.1-.8v-4.07c0-1.63-1.02-2.65-2.65-2.65H5.4c-.12 0-.23.01-.34.02a.799.799 0 0 1-.57-.2.72.72 0 0 1-.24-.55V6.26c0-2.94 2.06-5 5-5h8.5c2.94 0 5 2.06 5 5v5.1c0 1.45-.49 2.73-1.39 3.61-.72.73-1.72 1.2-2.86 1.34v1.11c0 .6-.33 1.14-.85 1.42-.24.11-.5.17-.75.17Zm-.6-1.88.65.37c.06-.03.06-.08.06-.09V15.6c0-.41.34-.75.75-.75 1.05 0 1.94-.33 2.55-.95.63-.62.95-1.5.95-2.55v-5.1c0-2.13-1.37-3.5-3.5-3.5h-8.5c-2.13 0-3.5 1.37-3.5 3.5v2h6.44c2.44 0 4.15 1.71 4.15 4.15v4.07c-.01.23-.02.45-.05.66Z"
      />
      <path
        fill="#6E6E6E"
        d="M6.07 22.75c-.22 0-.45-.05-.66-.16-.47-.25-.76-.73-.76-1.27v-.76c-.88-.14-1.66-.51-2.24-1.09-.76-.76-1.16-1.8-1.16-3V12.4c0-2.26 1.48-3.92 3.68-4.13.16-.01.31-.02.47-.02h6.79c2.44 0 4.15 1.71 4.15 4.15v4.07c0 .44-.05.85-.16 1.22-.45 1.8-1.98 2.93-3.99 2.93H9.7L6.87 22.5c-.24.17-.52.25-.8.25Zm-.67-13c-.12 0-.23.01-.34.02-1.44.13-2.31 1.12-2.31 2.63v4.07c0 .8.25 1.47.72 1.94.46.46 1.13.71 1.93.71.41 0 .75.34.75.75v1.31l2.9-1.93c.12-.08.27-.13.42-.13h2.72c1.32 0 2.25-.66 2.54-1.82.07-.25.11-.53.11-.83V12.4c0-1.63-1.02-2.65-2.65-2.65H5.4Z"
      />
    </svg>
  );
};
export default RabbaiIcon;
