"use client";

import fsLogoNoText from "@/assets/icons/fs-logo-no-text.png";
import notificationsIcon from "@/assets/icons/notifications.svg";
import settingsIcon from "@/assets/icons/settings.svg";
import SearchInput from "@/components/common/SearchInput";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";

const TopBar = () => {
  // To toggle between sign in button and user avatar
  const [authenticated, setAuthenticated] = useState(false);

  return (
    <div className="fixed top-0 z-[10] flex h-20 w-full items-center justify-between gap-2 bg-[#020213] px-5 py-4 lg:w-[calc(100%-5rem)] lg:px-12">
      <SearchInput className="hidden lg:block" placeholder="Search" hasMicrophone />

      <div className="flex items-center gap-2 sm:gap-4">
        <Image src={fsLogoNoText} alt="Fountain Stream Logo" className="block h-11 w-11 lg:hidden" />
        <Button className="h-8 w-13 sm:w-auto lg:h-auto" variant="tab">
          Today
        </Button>
        <Button variant="tab" className="h-8 w-24 bg-[#323240] text-white opacity-50 sm:w-auto lg:h-auto">
          Community
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <div className="hidden h-10 w-10 cursor-pointer items-center justify-center rounded-[6px] bg-[#101828] lg:flex">
          <Image src={settingsIcon} alt="Settings Icon" />
        </div>
        {authenticated && (
          <div className="flex h-10 w-10 cursor-pointer items-center justify-center rounded-[6px] bg-[#101828]">
            <Image src={notificationsIcon} alt="Notifications Icon" />
          </div>
        )}
        {authenticated ? (
          <div className="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-[#101828]">
            <Image src="https://bit.ly/ryan-florence" width={40} height={40} alt="Notifications Icon" className="rounded-full" />
          </div>
        ) : (
          <Button className="h-8 w-16 lg:h-11 lg:w-28" onClick={() => setAuthenticated(true)}>
            Sign in
          </Button>
        )}
      </div>
    </div>
  );
};

export default TopBar;
