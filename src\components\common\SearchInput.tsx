import microphoneIcon from "@/assets/icons/microphone.svg";
import searchIcon from "@/assets/icons/search.svg";
import { SearchInputProps } from "@/components/common/types";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { Input } from "../ui/input";

const SearchInput = ({ className, placeholder, hasMicrophone = false }: SearchInputProps) => {
  return (
    <div className="relative">
      <Input
        className={cn("h-[2.6rem] w-[33.4rem] border border-[#E5E7EB3B] bg-[#E5E7EB0A] px-10 placeholder:font-light", className)}
        placeholder={placeholder}
      />
      <Image src={searchIcon} alt="Search Icon" className="absolute top-1/2 left-4 -translate-y-1/2" />
      {hasMicrophone && <Image src={microphoneIcon} alt="Microphone Icon" className="absolute top-1/2 right-4 -translate-y-1/2" />}
    </div>
  );
};

export default SearchInput;
