/* eslint-disable react/no-unescaped-entities */
import fountainStreamLogo from "@/assets/images/fountain-stream.png";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const GetStarted = () => {
  return (
    <section className="mx-0 mt-12 flex flex-col items-center justify-between gap-6 bg-[#3333331F] py-4 pr-10 pl-10 md:mx-10 lg:flex-row lg:pl-0">
      <div className="flex flex-col items-center gap-3 md:flex-row">
        <Image src={fountainStreamLogo} alt="Fountain Stream Logo" width={124} height={124} />
        <div className="space-y-2">
          <p className="text-center text-[1.75rem] font-bold md:text-left">Enjoy what this Stream has to offer</p>
          <p className="text-center text-[#FFFFFF99] md:text-left">Let's help you with your feeding by customizing your meals</p>
        </div>
      </div>
      <Button className="h-[3rem] w-full md:ml-10 md:w-[70%] lg:w-[16rem]">Get Started</Button>
    </section>
  );
};

export default GetStarted;
