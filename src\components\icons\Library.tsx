import { IconProps } from "@/components/common/types";

const LibraryIcon = ({ isActive = false, ...svgProps }: IconProps) => {
  if (isActive) {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width={25} height={24} fill="none" {...svgProps}>
        <path
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeMiterlimit={10}
          strokeWidth={1.5}
          d="M17.9 22h-10c-3 0-5-1.5-5-5v-5c0-3.5 2-5 5-5h10c3 0 5 1.5 5 5v5c0 3.5-2 5-5 5ZM6.9 4.5h12M9.9 2h6"
        />
        <path
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M9.79 19.11a1.37 1.37 0 1 0 0-2.74 1.37 1.37 0 0 0 0 2.74ZM14.81 18.2a1.37 1.37 0 1 0 0-2.74 1.37 1.37 0 0 0 0 2.74ZM11.16 13.83l5.02-1.37"
        />
      </svg>
    );
  }
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={25} height={24} fill="none" {...svgProps}>
      <path
        stroke="#676D75"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeMiterlimit={10}
        strokeWidth={1.5}
        d="M17.9 22h-10c-3 0-5-1.5-5-5v-5c0-3.5 2-5 5-5h10c3 0 5 1.5 5 5v5c0 3.5-2 5-5 5ZM6.9 4.5h12M9.9 2h6"
      />
      <path
        stroke="#676D75"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M9.79 19.11a1.37 1.37 0 1 0 0-2.74 1.37 1.37 0 0 0 0 2.74ZM14.81 18.2a1.37 1.37 0 1 0 0-2.74 1.37 1.37 0 0 0 0 2.74ZM11.16 13.83l5.02-1.37"
      />
    </svg>
  );
};

export default LibraryIcon;
