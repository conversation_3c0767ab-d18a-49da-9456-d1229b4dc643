{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "tailwind.config.ts", "css": "src/app/globals.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"assets": "@/assets", "components": "@/components", "config": "@/config", "data": "@/data", "features": "@/features", "hooks": "@/hooks", "lib": "@/lib", "ui": "@/components/ui", "utils": "@/lib/utils", "schemas": "@/schemas", "templates": "@/templates", "types": "@/types"}, "iconLibrary": "lucide"}