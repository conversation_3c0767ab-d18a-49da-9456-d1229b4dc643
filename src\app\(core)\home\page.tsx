"use client";
import FloatingMenu from "@/components/home/<USER>";
import GetStarted from "@/components/home/<USER>";
import Ministers from "@/components/home/<USER>";
import NewbieMessages from "@/components/home/<USER>";
import NewReleases from "@/components/home/<USER>";
import NowLive from "@/components/home/<USER>";
import Popular from "@/components/home/<USER>";
import Recommendations from "@/components/home/<USER>";
import Shorts from "@/components/home/<USER>";
import Updates from "@/components/home/<USER>";

const Home = () => {
  return (
    <div className="relative">
      <FloatingMenu />
      <div className="min-h-dvh px-5 lg:px-12">
        <NowLive />
        <div className="bg-gradient-to-b from-transparent via-[#CF34000F] to-transparent">
          <NewbieMessages />
          <Updates />
          <Recommendations />
          <GetStarted />
          <NewReleases />
          <Shorts />
        </div>
        <Ministers />
        <Popular />
      </div>
    </div>
  );
};

export default Home;
