import { IconProps } from "@/components/common/types";

const DownloadIcon = ({ isActive = false, ...svgProps }: IconProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={14} height={14} fill="none" {...svgProps}>
      <path
        stroke="#fff"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.001}
        d="M9.468 5.145c2.002.172 2.82 1.2 2.82 3.453v.072c0 2.486-.996 3.482-3.482 3.482h-3.62c-2.486 0-3.482-.996-3.482-3.482v-.072c0-2.236.806-3.264 2.775-3.448M6.999 1.308V8.47"
      />
      <path stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.001} d="M8.862 7.23 6.999 9.094 5.136 7.23" />
    </svg>
  );
};
export default DownloadIcon;
