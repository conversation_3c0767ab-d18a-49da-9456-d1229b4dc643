import { IconProps } from "@/components/common/types";

const SendIcon = ({ isActive = false, ...svgProps }: IconProps) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={22} height={23} fill="none" {...svgProps}>
      <path
        fill="#fff"
        d="M3.234 20.186h15.531a.69.69 0 0 1 .685.685.69.69 0 0 1-.685.685H3.235a.69.69 0 0 1-.686-.685.69.69 0 0 1 .685-.685ZM17.395 14.037a.69.69 0 0 1-.685-.685V5.623L5.089 17.243a.677.677 0 0 1-.484.202.677.677 0 0 1-.484-.201.69.69 0 0 1 0-.968L15.74 4.655H8.012a.69.69 0 0 1-.685-.685.69.69 0 0 1 .685-.686h9.383c.091 0 .174.019.265.055a.653.653 0 0 1 .365.366.689.689 0 0 1 .055.265v9.382a.69.69 0 0 1-.685.685Z"
      />
    </svg>
  );
};
export default SendIcon;
