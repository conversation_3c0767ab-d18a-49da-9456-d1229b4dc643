import moreIcon from "@/assets/icons/more.svg";
import ReusableCarousel from "@/components/common/ReusableCarousel";
import { shorts } from "@/data/shorts";
import { Short } from "@/types";
import Image from "next/image";

const Shorts = () => {
  const renderShort = (short: Short) => {
    return (
      <div className="relative w-80">
        <Image src={short.image} alt={`${short.title}`} className="h-109 rounded-[8px] object-cover" />

        <div className="mt-4 w-full space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-base">PREPARE TO RECEIVE THE SON AT (#EGFMWTV) OCTOBER EDITION</p>
            <Image src={moreIcon} alt="More icon" width={18} height={18} className="cursor-pointer justify-self-end" />
          </div>
          <div className="flex flex-col items-start gap-2 text-xs font-light text-[#FFFFFFB8] lg:flex-row">
            <p>1.13k listens</p>
            <span className="hidden lg:block">|</span>
            <p>3 weeks ago</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <ReusableCarousel<Short>
      title="F.S Shorts"
      items={shorts}
      renderItem={renderShort}
      itemWidth="basis-[70%] sm:basis-[45%] lg:basis-[10%] xl:basis-[21%]"
    />
  );
};

export default Shorts;
