import { SVGProps } from "react";

const StreamVideoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={20} height={20} fill="none" viewBox="0 0 20 20" {...props}>
    <path
      fill="#fff"
      d="M12.5 18.958h-5c-4.525 0-6.458-1.933-6.458-6.458v-5c0-4.525 1.933-6.458 6.458-6.458h5c4.525 0 6.459 1.933 6.459 6.458v5c0 4.525-1.934 6.458-6.459 6.458m-5-16.666c-3.841 0-5.208 1.366-5.208 5.208v5c0 3.842 1.367 5.208 5.208 5.208h5c3.842 0 5.209-1.366 5.209-5.208v-5c0-3.842-1.367-5.208-5.209-5.208z"
    ></path>
    <path fill="#fff" d="M17.9 6.55H2.1a.624.624 0 1 1 0-1.25h15.8a.624.624 0 1 1 0 1.25"></path>
    <path
      fill="#fff"
      d="M7.1 6.433a.63.63 0 0 1-.625-.625v-4.05a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v4.05a.63.63 0 0 1-.625.625M12.9 6.058a.63.63 0 0 1-.625-.625V1.758a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.675c0 .35-.275.625-.625.625M9.242 15.1c-.3 0-.584-.075-.842-.217-.567-.333-.9-1-.9-1.841v-2c0-.842.333-1.517.908-1.85s1.325-.284 2.05.141l1.734 1c.725.417 1.15 1.042 1.15 1.709 0 .666-.425 1.291-1.159 1.708l-1.733 1c-.4.233-.817.35-1.208.35m.008-4.875a.44.44 0 0 0-.217.05c-.175.1-.283.383-.283.767v2c0 .375.108.658.283.766.175.1.475.05.8-.141l1.734-1c.325-.192.525-.425.525-.625s-.192-.434-.525-.625l-1.734-1c-.216-.125-.425-.192-.583-.192"
    ></path>
  </svg>
);

export default StreamVideoIcon;
