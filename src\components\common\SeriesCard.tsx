import Image from "next/image";
import { SeriesCardProps } from "./types";

const SeriesCard = ({ image, seriesTitle, messageCount }: SeriesCardProps) => {
  return (
    <div className="relative h-66 w-full max-w-70 overflow-hidden rounded-lg md:w-auto">
      <Image src={image} alt={`${seriesTitle} image`} className="h-full w-full object-cover" />
      <div className="absolute bottom-0 w-full bg-gradient-to-r from-[#100f0f03] via-[#FFFFFF1A] to-[#100f0f03] p-4 backdrop-blur-xs">
        <p className="line-clamp-1 text-xl font-semibold">{seriesTitle}</p>
        <p className="text-emphasis line-clamp-1 text-base font-medium">{messageCount} Messages</p>
      </div>
    </div>
  );
};

export default SeriesCard;
