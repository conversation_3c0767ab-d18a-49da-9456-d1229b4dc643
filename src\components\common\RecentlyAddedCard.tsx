import Image from "next/image";
import { Message } from "./types";
import PlayStyleIcon from "@/components/icons/PlayStyle";

const RecentlyAddedCard = ({ image, messageTitle, minister, meetingName }: Message) => {
  return (
    <>
      <div className="relative h-41 w-54.5 overflow-hidden rounded-lg">
        <Image src={image} alt={`${messageTitle} image`} className="h-full w-full object-cover" />
        <div className="absolute bottom-0 left-1 flex size-15 items-center justify-center rounded-full bg-[#1212129c] blur-sm" />
        <PlayStyleIcon className="absolute bottom-4 left-5 cursor-pointer" />
      </div>
      <div className="mt-4">
        <p className="line-clamp-1 text-base font-semibold">{messageTitle}</p>

        <div className="flex items-center justify-between text-sm font-light text-[#FFFFFFB8]">
          <div className="flex items-center gap-1.5">
            <p className="line-clamp-1 text-sm font-normal">{minister.name}</p>
            <div className="size-1 rounded-full bg-[#808089]" />
            <p className="line-clamp-1">{meetingName}</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default RecentlyAddedCard;
