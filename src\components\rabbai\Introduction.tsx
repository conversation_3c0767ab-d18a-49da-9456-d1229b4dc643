import reverend<PERSON>ab<PERSON><PERSON>Image from "@/assets/images/reverend-kayode-rabbai.png";
import { Button } from "@/components/ui/button";
import Image from "next/image";

const Introduction = () => {
  const handleGetStarted = () => {};

  return (
    <div className="px-4 md:px-8 lg:flex-row lg:px-14">
      <div className="text-[2rem] font-bold sm:text-[3rem] md:text-[4rem] lg:text-[5.5rem]">
        <span>Introducing Rabb</span>
        <span className="text-[#FBBC04]">AI</span>
      </div>
      <div className="mt-10 flex flex-col gap-8 lg:flex-row lg:items-center lg:gap-12 xl:gap-20">
        <div className="h-[30.6rem] w-full flex-1 rounded-2xl bg-[#3333331F] px-5 py-[0.9rem]">
          <Image src={reverendRab<PERSON>iImage} alt="<PERSON>" className="h-full w-full rounded-2xl object-cover" />
        </div>
        <div className="w-full flex-1 lg:w-[50%]">
          <p className="font-light text-[#FFFFFFB0] md:text-[1.25rem] lg:text-2xl">
            Introducing the <span className="font-bold text-white">RabbAI</span>, your messaging listening guide assistant where you can do a whole
            lot and can help you as your keep <span className="font-bold text-white"> Listening Twice.</span>
          </p>
          <p className="mt-6 font-light text-[#FFFFFFB0] md:text-[1.25rem] lg:text-2xl">
            With this feature, you can take a deep search on messages you need, ask anything and get a well detailed answer , create a concise and
            well detailed study guide to help as you study the Word of Truth.
          </p>
          <p className="mt-6 font-light text-[#FFFFFFB0] md:text-[1.25rem] lg:text-2xl">Keep Listening and Enjoy RabbAI</p>

          <Button className="mt-10 h-[3rem] w-full bg-[#B88C00] text-[#191928] lg:w-[27rem]" onClick={handleGetStarted}>
            Get Started
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Introduction;
