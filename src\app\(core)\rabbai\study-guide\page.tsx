"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { studyGuideSchema } from "@/schemas/rabbai";
import { StudyGuideFormData } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";

const StudyGuide = () => {
  const router = useRouter();
  const form = useForm<StudyGuideFormData>({
    resolver: zodResolver(studyGuideSchema),
    defaultValues: {
      topic: "",
      description: "",
    },
  });

  const { getValues } = form;

  const handleAskRabbai = (data: StudyGuideFormData) => {
    router.push("/rabbai/study-guide/1");
  };

  return (
    <div className="w-full px-[1rem] sm:px-[2rem] md:px-[4rem] lg:w-[30rem] xl:w-[40rem]">
      <div className="mt-[1rem] mb-10 lg:mt-[3rem]">
        <p className="text-center text-[1.25rem] font-bold md:text-2xl">RabbAI Study Guide</p>
        <p className="mt-2 text-center font-light text-[#FFFFFFB0]">
          Prepare a study guide on a particular topic. Enter your topic and give a detailed description of the guide
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleAskRabbai)} className="space-y-5">
          <FormField
            control={form.control}
            name="topic"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder="Topic" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea className="mt-5 h-[14rem] rounded-[8px]" placeholder="Detailed Description" {...field}></Textarea>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            className="bg-primary mt-5 h-[3rem] w-full font-semibold text-[#191928] disabled:bg-[#E9D7FE4D]"
            type="submit"
            disabled={!(getValues("topic") && getValues("description"))}
          >
            Submit
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default StudyGuide;
