import { cn } from "@/lib/utils";
import Select from "react-select";

const SelectField = ({
  className,
  label,
  placeholder,
  options,
}: {
  className?: string;
  label?: string;
  placeholder: string;
  options: { label: string | number; value: string | number }[];
}) => {
  return (
    <div className="flex flex-col gap-2 sm:gap-4">
      <label className="text-sm font-medium text-white/50 sm:text-base">{label}</label>
      <Select
        classNames={{
          input: () => "h-10",
          singleValue: () => "text-white/40!",
          control: () => "bg-white/4! rounded-lg! border-0.5 border-[#D0D5DD]/16!",
          placeholder: () => "text-white/40!",
          indicatorSeparator: () => "hidden",
          dropdownIndicator: () => "text-white/40!",
          menu: () => "bg-[#1e1c2b]!",
          option: () => "text-white/40! text-sm!",
        }}
        className={cn("focus:shadow-none! focus:ring-0!", className)}
        placeholder={placeholder}
        styles={{
          control: styles => ({
            ...styles,
            boxShadow: "none",
          }),
          option: (styles, state) => ({
            ...styles,
            backgroundColor: state.isFocused ? "black" : "#1e1c2b",
          }),
        }}
        options={options}
      />
    </div>
  );
};

export default SelectField;
