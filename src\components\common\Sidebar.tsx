"use client";
import fsLogo from "@/assets/icons/fs-logo.png";
import HomeIcon from "@/components/icons/Home";
import LibraryIcon from "@/components/icons/Library";
import MeetingsIcon from "@/components/icons/Meetings";
import MessagesIcon from "@/components/icons/Messages";
import RabbaiIcon from "@/components/icons/Rabbai";
import NavLink from "@/components/common/NavLink";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { usePathname } from "next/navigation";

const sidebarLinks = [
  {
    icon: HomeIcon,
    href: "/home",
    name: "Home",
  },
  {
    icon: RabbaiIcon,
    href: "/rabbai",
    name: "<PERSON>bb<PERSON><PERSON>",
  },
  {
    icon: MessagesIcon,
    href: "/messages",
    name: "Messages",
  },
  {
    icon: MeetingsIcon,
    href: "/meetings",
    name: "Meetings",
  },
  {
    icon: LibraryIcon,
    href: "/library",
    name: "Library",
  },
];

const Sidebar = () => {
  const pathname = usePathname();
  const paths = pathname.split("/");

  return (
    <div className="fixed top-0 left-0 z-10 hidden h-dvh w-[5rem] bg-[#32324029] px-4 py-8 lg:block">
      <Image src={fsLogo} alt="Fountain Stream Logo" className="h-[3.125rem] w-full" />
      <nav className="mt-10 flex flex-col gap-2">
        {sidebarLinks.map((link, i) => {
          const Icon = link.icon;
          const isActive = paths[1] === String(link.href).slice(1);
          return (
            <NavLink key={i} href={link.href} className={cn("justify-center py-2.5 hover:bg-[#676D752E]", isActive && "bg-[#676D752E]")}>
              <Icon isActive={isActive} />
            </NavLink>
          );
        })}
      </nav>
    </div>
  );
};

export default Sidebar;
