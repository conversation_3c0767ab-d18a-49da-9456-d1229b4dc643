# Fountain Stream Web V3

The form builder is an application used to build forms for simple and advanced activities in Babban Gona

## Installation

1. **Clone the repository:**

   ```bash
   git clone https://gitlab.com/fountainstream-web/frontend/web/web-v3.git
   ```

2. **Install dependencies:**

   ```bash
   pnpm i
   ```

## Usage

Start the server for development using the following command:

```bash
pnpm dev
```

For production, build and start the server using the following commands respectively:

```bash
pnpm build
```

```bash
pnpm start
```

## Configuration

Before starting the application, make sure to set up the required environment variables in a `.env` file. You can use the `.env.example` file as a template:

```bash
cp .env.example .env
```
