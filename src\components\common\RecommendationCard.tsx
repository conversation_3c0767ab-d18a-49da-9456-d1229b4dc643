import moreIcon from "@/assets/icons/more.svg";
import { getNumberInCompactFormat } from "@/lib/utils";
import Image from "next/image";
import { Message } from "./types";

const RecommendationCard = ({ id, image, messageTitle, numberOfListens, date, minister }: Message) => {
  return (
    <div>
      <div className="h-59 overflow-hidden rounded-lg">
        <Image src={image} alt={`${messageTitle} image`} className="h-full w-full object-cover" />
      </div>
      <div className="mt-4 space-y-2">
        <div className="flex items-center justify-between">
          <p className="line-clamp-1 text-[1.125rem]">{messageTitle}</p>
          <Image src={moreIcon} alt="More icon" width={18} height={18} className="cursor-pointer" />
        </div>
        <div className="flex items-center justify-between text-sm font-light text-[#FFFFFFB8]">
          <div className="flex items-center gap-2">
            <p>{`${getNumberInCompactFormat(numberOfListens)} listen${numberOfListens > 1 ? "s" : ""}`}</p>|<p>{date}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Image src={minister.image} alt={`${minister.name} image`} className="h-4 w-4 rounded-full border border-white object-cover" />
          <p className="line-clamp-1 text-sm font-light text-[#FFFFFFB8]">{minister.name}</p>
        </div>
      </div>
    </div>
  );
};

export default RecommendationCard;
