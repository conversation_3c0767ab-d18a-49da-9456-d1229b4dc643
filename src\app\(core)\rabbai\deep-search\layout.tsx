"use client";
import ConversationHistory from "@/components/rabbai/ConversationHistory";
import { studyGuideConversations } from "@/data/rabbai";
import { useState } from "react";
import { HiMenuAlt2 } from "react-icons/hi";

const DeepSearchLayout = ({ children }: { children: React.ReactNode }) => {
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(false);
  return (
    <div>
      <ConversationHistory
        conversations={studyGuideConversations}
        isHistoryOpen={isHistoryOpen}
        setIsHistoryOpen={setIsHistoryOpen}
        route="/rabbai/deep-search"
      />
      <HiMenuAlt2 size={24} className="fixed top-[5rem] left-6 size-6 lg:hidden" onClick={() => setIsHistoryOpen(true)} />
      <div className="flex-1 px-4 lg:ml-[25rem] lg:px-0 xl:ml-[30rem] 2xl:ml-[35rem]">{children}</div>
    </div>
  );
};

export default DeepSearchLayout;
