import { IconProps } from "@/components/common/types";

const DownloadUpdateIcon = ({ isActive = false, ...svgProps }: IconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={23} height={23} fill="none" {...svgProps}>
    <path
      fill="#fff"
      d="M14.245 21.1H8.29c-4.486 0-6.405-1.92-6.405-6.405v-.119c0-4.056 1.6-6.011 5.18-6.35a.705.705 0 0 1 .75.622.687.687 0 0 1-.622.75c-2.868.264-3.937 1.616-3.937 4.987v.119c0 3.718 1.315 5.034 5.034 5.034h5.956c3.718 0 5.034-1.316 5.034-5.034v-.119c0-3.39-1.087-4.741-4.01-4.988a.684.684 0 0 1 .118-1.361c3.636.31 5.262 2.275 5.262 6.358v.12c0 4.467-1.918 6.385-6.404 6.385Z"
    />
    <path fill="#fff" d="M11.267 15.051a.69.69 0 0 1-.685-.685V2.6a.69.69 0 0 1 .685-.685.69.69 0 0 1 .685.685v11.767c0 .384-.31.685-.685.685Z" />
    <path
      fill="#fff"
      d="M11.267 16.074a.678.678 0 0 1-.484-.2l-3.06-3.061a.69.69 0 0 1 0-.969.69.69 0 0 1 .968 0l2.576 2.577 2.576-2.576a.69.69 0 0 1 .969 0 .69.69 0 0 1 0 .968l-3.061 3.06a.677.677 0 0 1-.484.201Z"
    />
  </svg>
);
export default DownloadUpdateIcon;
