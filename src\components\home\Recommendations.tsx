import { recommendations } from "@/data/recommendations";
import RecommendationCard from "../common/RecommendationCard";

const Recommendations = () => {
  return (
    <section className="mx-0 mt-12 sm:mx-10">
      <p className="text-lg font-bold sm:text-2xl">Recommended For You</p>
      <div className="mt-8 grid grid-cols-1 gap-x-5 gap-y-10 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {recommendations.map(recommendation => (
          <RecommendationCard key={recommendation.id} {...recommendation} />
        ))}
      </div>
    </section>
  );
};

export default Recommendations;
