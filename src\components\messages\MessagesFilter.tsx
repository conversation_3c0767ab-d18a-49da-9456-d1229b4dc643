import { tags } from "@/data/tags";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { Button } from "../ui/button";
import SelectField from "../ui/select";
import { Tag } from "./types";
import { getYears } from "@/lib/utils";

const MessagesFilter = () => {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const years = getYears();

  const selectTag = (tag: string, evt: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    evt.preventDefault();
    setSelectedTags(prevTags => [...prevTags, tag]);
  };

  return (
    <>
      <div className="flex flex-col gap-8 px-5 sm:gap-14 sm:px-12">
        <p className="mt-4 text-lg font-medium sm:mt-16 sm:text-2xl">Filter by</p>

        <form className="flex flex-col gap-5 sm:gap-10">
          <SelectField label="By Year" placeholder="Select Year" options={years} />
          <SelectField label="By Month" placeholder="Select Month" options={[]} />
          <SelectField label="By Meetings" placeholder="Select Meetings" options={[]} />
          <SelectField label="By Minister" placeholder="Select Minister" options={[]} />

          <div className="mb-6.5">
            <label className="text-sm font-medium text-white/50 sm:text-base">By Tags</label>
            <div className="mt-4 space-y-4 space-x-4">
              {tags().map((tag: Tag) => (
                <Button
                  onClick={evt => selectTag(tag.name, evt)}
                  key={tag.id}
                  className={cn("rounded-[5px] bg-white/10 text-sm text-white", selectedTags.includes(tag.name) && "bg-[#D0D5DD]/98 text-[#1e1c2b]")}
                >
                  {tag.name}
                </Button>
              ))}
            </div>
          </div>
        </form>
      </div>
      <div className="bottom-0 left-0 flex h-24 w-full items-center justify-center gap-3 bg-[#1e1c2b] px-6">
        <Button className="border border-white/39 bg-[#1e1c2b] px-10 py-4 text-white sm:px-18 sm:py-6">Reset All</Button>
        <Button className="px-10 py-4 text-[#1e1c2b] sm:px-18 sm:py-6">Apply Filter</Button>
      </div>
    </>
  );
};

export default MessagesFilter;
