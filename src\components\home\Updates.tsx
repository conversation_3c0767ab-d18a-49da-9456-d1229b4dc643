"use client";
import DownloadUpdateIcon from "@/components/icons/DownloadUpdate";
import HeartIcon from "@/components/icons/Heart";
import SendIcon from "@/components/icons/Send";
import ReusableCarousel from "@/components/common/ReusableCarousel";
import { updates } from "@/data/updates";
import { Update } from "@/types";
import Image from "next/image";

const Updates = () => {
  const renderUpdate = (update: Update) => {
    return (
      <div className="relative -mr-10 h-[22.5rem] w-screen overflow-hidden rounded-2xl">
        <div className="absolute inset-0 z-10 rounded-2xl bg-gradient-to-b from-[#02021300] to-[#020213]" />
        <Image src={update.image} alt={`${update.title} image`} className="absolute inset-0 object-cover" fill />

        <div className="absolute bottom-10 z-20 w-full space-y-3 pr-8">
          <div className="flex flex-col justify-between gap-5 lg:flex-row">
            <div className="flex flex-col gap-3 px-10">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-[#D9D9D9] sm:h-5 sm:w-5" />
                <p className="text-base sm:text-xl">{update.ministry}</p>
              </div>
              <p className="text-3xl font-semibold sm:text-5xl lg:text-6xl">{update.title}</p>
              <p className="text-lg font-light text-white opacity-56 lg:text-2xl">{update.details}</p>
            </div>

            <div className="right-10 bottom-16 z-20 ml-8 flex items-center gap-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#D9D9D91A]">
                <HeartIcon />
              </div>
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#D9D9D91A]">
                <SendIcon />
              </div>
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#D9D9D91A]">
                <DownloadUpdateIcon />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return <ReusableCarousel<Update> title="Latest Updates" items={updates} renderItem={renderUpdate} itemWidth="basis-full" />;
};

export default Updates;
