# .github/workflows/mirror-to-gitlab.yml
name: Mirror to Git<PERSON>ab

on:
  push:
    branches:
      - gitlab   # or whatever branch you want mirrored

jobs:
  mirror:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0    # need full history to push tags, etc.

      - name: Push to GitLab
        env:
          GITLAB_PAT: ${{ secrets.GITLAB_PAT }}
        run: |
          git remote add gitlab \
            https://oauth2:${GITLAB_PAT}@gitlab.com/fountainstream-web/frontend/web/web-v3.git
          # push the current branch:
          git push gitlab HEAD:working --force
