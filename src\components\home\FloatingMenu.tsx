import bookIcon from "@/assets/icons/book.svg";
import musicIcon from "@/assets/icons/music.svg";
import plusIcon from "@/assets/icons/plus.svg";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

const FloatingMenu = () => {
  const router = useRouter();
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean>(false);

  const handlePopoverOpen = () => {
    setIsPopoverOpen(!isPopoverOpen);
  };

  const handleRabbaiOpen = () => {
    router.push("/rabbai");
  };

  return (
    <div className="fixed right-7 bottom-25 z-10 space-y-4 sm:right-10 lg:right-20 lg:bottom-10">
      <div className={`flex flex-col gap-4 ${isPopoverOpen ? "opacity-100" : "opacity-0"} transition-opacity duration-200 ease-in-out`}>
        <Button
          className="gap-5 rounded-[50px] bg-gradient-to-r from-[#EACDA3] to-[#E6B980] py-6 text-xl font-normal text-black md:h-[4.75rem] md:w-[21rem] md:py-0 md:text-[1.75rem]"
          onClick={handleRabbaiOpen}
        >
          <Image src={bookIcon} alt="Book Icon" />
          <p>Open RabbAI</p>
        </Button>
        <Button className="gap-5 rounded-[50px] bg-gradient-to-r from-[#FFC796] to-[#FF6B95] py-6 text-xl font-normal text-black md:h-[4.75rem] md:w-[21rem] md:py-0 md:text-[1.75rem]">
          <Image src={musicIcon} alt="Music Icon" />
          <p>Switch to Music</p>
        </Button>
      </div>
      <div className="flex justify-end">
        <Button
          className="flex h-16 w-16 cursor-pointer items-center justify-center rounded-full bg-[#FBBC04] md:h-26 md:w-26"
          onClick={handlePopoverOpen}
        >
          <Image
            src={plusIcon}
            alt="Plus icon"
            className={`h-[3.25rem] w-[3.25rem] ${isPopoverOpen ? "rotate-45" : "rotate-0"} transition duration-200 ease-in-out`}
          />
        </Button>
      </div>
    </div>
  );
};

export default FloatingMenu;
