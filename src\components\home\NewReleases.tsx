import moreIcon from "@/assets/icons/more.svg";
import ReusableCarousel from "@/components/common/ReusableCarousel";
import { releases } from "@/data/releases";
import Image from "next/image";
import { Release } from "../../types";

const NewReleases = () => {
  const renderRelease = (release: Release) => {
    return (
      <div className="w-72.5">
        <div className="relative h-48 overflow-hidden rounded-lg">
          <Image src={release.image} alt={`${release.messageTitle} image`} className="h-full w-full object-cover" />
          <div className="absolute right-0 bottom-0 rounded-tl-lg bg-gradient-to-r from-[#000000A6] to-[#6666667D] px-4 py-1 text-sm">
            <p className="text-xxs">{release.duration}</p>
          </div>
        </div>
        <div className="mt-4 w-full space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-base">{release.messageTitle}</p>
            <Image src={moreIcon} alt="More icon" width={18} height={18} className="cursor-pointer justify-self-end" />
          </div>
          <div className="flex flex-col items-start gap-2 text-xs font-light text-[#FFFFFFB8] lg:flex-row">
            <Image
              src={release.minister.image}
              alt={`${release.minister.name} image`}
              className="hidden h-4 w-4 rounded-full border border-white object-cover lg:block"
            />
            <p>{release.minister.name}</p>
            <span className="hidden lg:block">|</span>
            <p>{release.date}</p>
          </div>
        </div>
      </div>
    );
  };
  return (
    <ReusableCarousel<Release>
      title="New Releases"
      items={releases}
      renderItem={renderRelease}
      itemWidth="basis-[60%] sm:basis-[27%] xl:basis-[26%] 2xl:basis-[23%]"
    />
  );
};

export default NewReleases;
