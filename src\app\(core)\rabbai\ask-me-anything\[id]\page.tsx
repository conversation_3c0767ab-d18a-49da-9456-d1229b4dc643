import calendarIcon from "@/assets/icons/calendar.svg";
import documentDownloadIcon from "@/assets/icons/document-download.svg";
import referenceIcon from "@/assets/icons/reference.svg";
import timerIcon from "@/assets/icons/timer.svg";
import { Button } from "@/components/ui/button";
import { studyGuideResult } from "@/data/rabbai";
import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import { use } from "react";

const AskMeAnythingItem = ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = use(params);

  return (
    <div className="w-full px-[1rem] pt-[2rem] sm:px-[2rem] md:px-[4rem] lg:w-[45rem]">
      <div>
        <p className="text-center text-[1.25rem] font-bold md:text-2xl">Ask Me Anything</p>
        <p className="mt-2 text-center font-light text-[#FFFFFFB0]">
          Here is a detailed answer based on the question you asked: What does <PERSON> do as the High priest different from the minister of the
          sanctuary?
        </p>
      </div>
      <div className="mt-4 rounded-tr-[22px] rounded-br-[22px] rounded-bl-[22px] border border-[#FFFFFF24] bg-[#FFFFFF0D] p-4">
        <p className="text-2xl font-bold">{studyGuideResult.topic}</p>
        <div className="mt-4 flex items-center gap-4 text-sm font-semibold">
          <div className="flex items-center gap-2">
            <Image src={calendarIcon} alt="Calendar Icon" width={15} height={15} />
            <p className="text-primary">{format(studyGuideResult.created_at, "dd/MM/yyyy")}</p>
          </div>
          <div className="flex items-center gap-2">
            <Image src={timerIcon} alt="Timer Icon" width={15} height={15} />
            <p className="text-primary">{studyGuideResult.reading_time}</p>
          </div>
          <div className="flex items-center gap-2">
            <Image src={referenceIcon} alt="Reference Icon" width={15} height={15} />
            <p className="underline">{studyGuideResult.references.join("; ")}</p>
          </div>
        </div>
        <hr className="my-5 border-[#FFFFFF14]" />
        <div className="rounded-[3px] bg-[#FFFFFF0D] p-5">
          <p className="text-center text-sm font-medium">Memory Track</p>
          <p className="text-center text-sm">{`${studyGuideResult.memory_track.reference}: "${studyGuideResult.memory_track.text}"`}</p>
        </div>
        <Image src={studyGuideResult.image} alt={`${studyGuideResult.topic} image`} className="mt-5 h-[15rem] w-full rounded-[3px] object-cover" />
        <hr className="my-5 border-[#FFFFFF14]" />
        <div className="space-y-5">
          {studyGuideResult.sections.map(section => (
            <div key={section.title} className="rounded-[5px] border border-[#C4C4C461] bg-[#1717174D] p-5">
              <p className="text-lg font-bold">{`${section.title} (${section.reference})`}</p>
              <p className="mt-5 text-justify leading-[1.8rem]">{section.content}</p>
            </div>
          ))}
        </div>
        <div className="mt-5 rounded-[4px] bg-gradient-to-t from-[#0E0E0E] to-[#45171F]">
          <p className="text-medium bg-[#FFFFFF0A] p-2 text-center text-lg">Conclusion</p>
          <p className="px-[3rem] py-3 text-center text-sm">{studyGuideResult.conclusion}</p>
        </div>
        <p className="text-primary text-medium my-4 text-center">END OF THE LESSON</p>
        <Button className="h-12 w-full gap-3 rounded-[5px] bg-[#FFFFFF12] text-sm font-normal text-white">
          <p>Download this Guide as PDF</p>
          <Image src={documentDownloadIcon} alt="Document Download Icon" width={20} height={20} />
        </Button>
      </div>
      <div className="mt-[4rem] flex h-24 items-center gap-4 bg-[#FFFFFF05] px-4 lg:-mx-[6rem] xl:-mx-[12rem]">
        <Button className="h-12 flex-1 gap-3 rounded-[8px] border-[#FFFFFF63] bg-[#1E1C2B] text-white">Cancel</Button>
        <Link
          href="/rabbai/study-guide"
          className="flex h-12 flex-1 items-center justify-center gap-3 rounded-[8px] bg-white font-medium text-[#1E1C2B]"
        >
          Create Another
        </Link>
      </div>
    </div>
  );
};

export default AskMeAnythingItem;
