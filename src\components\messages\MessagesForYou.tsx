import { popularMessages } from "@/data/popular";
import NavLink from "../common/NavLink";
import RecommendationCard from "../common/RecommendationCard";

const MessagesForYou = ({ preview = false }: { preview?: boolean }) => {
  return (
    <section className="mx-0 mt-13 sm:mx-10">
      <div className="flex justify-between">
        <h1 className="text-lg font-bold sm:text-2xl">Messages For You</h1>
        {preview && (
          <NavLink href={"/messages/all"} className="bg-transparent pr-0">
            <p className="text-emphasis text-lg">See All</p>
          </NavLink>
        )}
      </div>

      <div className="mt-8 grid grid-cols-1 gap-x-5 gap-y-10 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {[...(preview ? popularMessages.slice(0, 10) : popularMessages)].map(message => (
          <RecommendationCard key={message.id} {...message} />
        ))}
      </div>
    </section>
  );
};

export default MessagesForYou;
