import { SVGProps, ReactNode } from "react";
import { StaticImageData } from "next/image";

export type IconProps = SVGProps<SVGSVGElement> & {
  isActive?: boolean;
};

export type ReusableCarouselProps<T> = {
  title: string;
  items: T[];
  renderItem: (item: T) => ReactNode;
  itemWidth: string;
};

export type CarouselItemData = {
  id: string | number;
};

export type SearchBarProps = {
  className?: string;
  placeholder?: string;
  rightIcon?: StaticImageData;
  onSubmit: () => void;
  onClose: () => void;
  autoFocus?: boolean;
};

export type SearchInputProps = {
  className?: string;
  placeholder?: string;
  hasMicrophone?: boolean;
};

export type Message = {
  id: number;
  messageTitle: string;
  image: StaticImageData;
  numberOfListens: number;
  meetingName: string;
  date: string;
  duration: string;
  minister: {
    name: string;
    image: StaticImageData;
  };
};

export type MessageListItemProps = {
  message: Message;
  index: number;
};

export type SeriesCardProps = {
  image: StaticImageData;
  seriesTitle: string;
  messageCount: number;
};
