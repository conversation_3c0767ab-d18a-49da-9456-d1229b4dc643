import { series } from "@/data/series";
import NavLink from "../common/NavLink";
import SeriesCard from "../common/SeriesCard";

const SeriesForYou = () => {
  return (
    <section className="mx-0 mt-13 sm:mx-10">
      <div className="flex justify-between">
        <h1 className="text-lg font-bold sm:text-2xl">Series For You</h1>
        <NavLink href={"/messages/all"} className="bg-transparent pr-0">
          <p className="text-emphasis text-lg">See All</p>
        </NavLink>
      </div>

      <div className="mt-6 grid grid-cols-1 justify-items-center gap-x-5 gap-y-10 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {series.map(group => (
          <SeriesCard key={group.id} {...group} />
        ))}
      </div>
    </section>
  );
};

export default SeriesForYou;
